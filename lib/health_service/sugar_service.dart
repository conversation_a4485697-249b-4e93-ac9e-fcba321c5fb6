import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_record.dart';
import 'package:health_diary/types/health_types.dart';

import 'health_service.dart';
import 'widgets/blood_sugar_form.dart';

class SugarService extends HealthService {
  @override
  HealthRecordEntry parseRecordEntry(HealthRecord record) {
    final timeFormatter = DateFormat('MM-dd HH:mm');
    final timeString = timeFormatter.format(record.createdAt);
    final sugar = record.data as BloodSugarData;

    return HealthRecordEntry(
      time: timeString,
      type: 'blood_sugar_type'.tr(),
      note: sugar.note ?? '',
      recordType: HealthRecordTypeEnum.bloodSugar,
      values: [
        HealthRecordValue(
          label: 'blood_sugar_label'.tr(),
          value: sugar.value.toStringAsFixed(1),
          unit: 'mmol/L',
          isAbnormal: _isBloodSugarAbnormal(sugar.value),
        ),
      ],
      color: 0xFFE4F4FF, // 蓝色
    );
  }

  @override
  bool canScan() => false;

  @override
  TodayHealthOverview calculateAverage(List<HealthRecord> records) {
    if (records.isEmpty) {
      return emptyOverview();
    }

    double totalValue = 0;
    for (final record in records) {
      final sugar = record.data as BloodSugarData;
      totalValue += sugar.value;
    }

    final avgValue = totalValue / records.length;
    final isAbnormal = _isBloodSugarAbnormal(avgValue);

    return TodayHealthOverview(
      value: avgValue.toStringAsFixed(1),
      unit: 'mmol',
      icon: FontAwesomeIcons.droplet,
      iconColor: Colors.lightBlue.withValues(alpha: 0.6),
      isAbnormal: isAbnormal,
    );
  }

  @override
  TodayHealthOverview emptyOverview() {
    return TodayHealthOverview(
      value: "--",
      unit: 'mmol',
      icon: FontAwesomeIcons.droplet,
      iconColor: Colors.lightBlue.withValues(alpha: 0.6),
    );
  }

  @override
  Widget buildInputForm({required GlobalKey<FormState> formKey}) {
    return BloodSugarForm(formKey: formKey);
  }

  @override
  HealthRecordData? parseScannerData(List<int> data) {
    return null;
  }

  /// 判断血糖是否异常
  /// 正常血糖范围：空腹 3.9-6.1 mmol/L，餐后2小时 < 7.8 mmol/L
  /// 这里采用综合标准：3.9-7.8 mmol/L 为正常范围
  bool _isBloodSugarAbnormal(double value) {
    return value < 3.9 || value > 7.8;
  }

  /// 根据当前时间自动计算血糖类型和时间点
  static BloodSugarTimeInfo calculateFromCurrentTime() {
    final now = DateTime.now();
    return calculateFromTime(now);
  }

  /// 根据指定时间计算血糖类型和时间点
  static BloodSugarTimeInfo calculateFromTime(DateTime dateTime) {
    final hour = dateTime.hour;

    // 根据时间判断时间点
    MealTimeEnum mealTime;
    BloodSugarTypeEnum bloodSugarType;
    debugPrint('hour: $hour');

    if (hour >= 6 && hour < 11) {
      // 6:00-10:59 早餐时间
      mealTime = MealTimeEnum.breakfast;
      if (hour < 7) {
        // 7点前认为是空腹
        bloodSugarType = BloodSugarTypeEnum.fasting;
      } else if (hour < 8) {
        // 7-8点认为是餐前
        bloodSugarType = BloodSugarTypeEnum.beforeMeal;
      } else {
        bloodSugarType = BloodSugarTypeEnum.afterMeal;
      }
    } else if (hour >= 11 && hour < 17) {
      // 11:00-16:59 午餐时间
      mealTime = MealTimeEnum.lunch;
      if (hour < 12) {
        // 12点前认为是餐前
        bloodSugarType = BloodSugarTypeEnum.beforeMeal;
      } else if (hour < 14) {
        // 12-13点认为是餐后
        bloodSugarType = BloodSugarTypeEnum.afterMeal;
      } else {
        // 14点后认为是餐前（下午茶时间或准备晚餐）
        bloodSugarType = BloodSugarTypeEnum.beforeMeal;
      }
    } else if (hour >= 17 && hour < 22) {
      // 17:00-21:59 晚餐时间
      mealTime = MealTimeEnum.dinner;
      if (hour < 18) {
        // 18点前认为是餐前
        bloodSugarType = BloodSugarTypeEnum.beforeMeal;
      } else if (hour < 20) {
        // 18-19点认为是餐后
        bloodSugarType = BloodSugarTypeEnum.afterMeal;
      } else {
        // 20点后认为是餐后
        bloodSugarType = BloodSugarTypeEnum.afterMeal;
      }
    } else {
      // 22:00-5:59 夜间时间
      if (hour >= 22 || hour < 2) {
        // 22点-1点认为是睡前时间
        mealTime = MealTimeEnum.bedtime;
        bloodSugarType = BloodSugarTypeEnum.afterMeal;
      } else {
        // 2点-5点认为是空腹（夜间或清晨）
        mealTime = MealTimeEnum.breakfast;
        bloodSugarType = BloodSugarTypeEnum.fasting;
      }
    }

    return BloodSugarTimeInfo(
      bloodSugarType: bloodSugarType,
      mealTime: mealTime,
    );
  }
}

/// 血糖时间信息
class BloodSugarTimeInfo {
  final BloodSugarTypeEnum bloodSugarType;
  final MealTimeEnum mealTime;

  const BloodSugarTimeInfo({
    required this.bloodSugarType,
    required this.mealTime,
  });
}
